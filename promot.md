完成backtest函数的实现，具体要求如下：

1. **函数参数说明**：
   - train_df: 训练数据集
   - test_df: 测试数据集  
   - model: 已训练的机器学习模型
   - name: 策略名称（用于图表标题）

2. **数据列说明**：
   - 'signal'列：交易信号标签，0表示预测价格下跌，1表示预测价格上涨
   - 'close_roc'列：收盘价变化率（price rate of change）

3. **回测逻辑**：
   - 使用模型对train_df和test_df进行预测，生成交易信号
   - 根据预测信号计算策略收益：当信号为1时做多，信号为0时做空或持现金
   - 计算累计收益率（cumulative PnL ratio）
   - 计算最大回撤（maximum drawdown）

4. **可视化要求**：
   - 创建双轴图表
   - 左轴：显示累计PnL比例的时间序列曲线
   - 右轴：显示最大回撤的时间序列曲线
   - 图表标题包含策略名称
   - 添加适当的图例和轴标签
   - 分别展示训练集和测试集的回测结果

5. **输出要求**：
   - 打印关键性能指标（如总收益率、最大回撤、夏普比率等）
   - 返回回测结果数据框或性能指标字典