{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Secondary Model\n", "- inputs\n", "    - labels: meta-label(outcome of primary model = trading strategy)\n", "    - features: same as momentum classifiers\n", "\n", "- models: SVM, Random Forest, Gradient Boosting, LSTM\n", "\n", "- outputs\n", "    - bet confidence\n", "\n", "- strategy enhancing\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# lib\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns;sns.set()\n", "plt.style.use('tableau-colorblind10')\n", "\n", "# different models\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier, VotingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.naive_bayes import GaussianNB\n", "\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, normalize\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import roc_auc_score, accuracy_score, f1_score, precision_score\n", "\n", "# homemade\n", "from feature_engineering import dimension_reduction as DR\n", "from features import tautil\n", "from labeling import labeling\n", "from backtest import round_trip\n", "from triple_barrier import make_rt\n", "\n", "from mlutil.pkfold import PKFold"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(action='ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# get X,y"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["market_df = pd.read_csv('C:data/market_samsung.csv')\n", "market_df = market_df.rename(columns={market_df.columns[0]:'Date'})\n", "market_df.index = pd.to_datetime(market_df.Date)\n", "market_df.drop(columns='Date',inplace=True)\n", "market_df.dropna(inplace=True)\n", "close = market_df.close['2010':'2020']\n", "\n", "feature_df = pd.read_csv('C:data/features_samsung.csv')\n", "feature_df = feature_df.rename(columns={feature_df.columns[0]:'Date'})\n", "feature_df.index = pd.to_datetime(feature_df.Date)\n", "feature_df.drop(columns='Date',inplace=True)\n", "feature_df.dropna(inplace=True)\n", "\n", "selected_features = pd.read_csv('C:data/selected_features.csv').columns[1:]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["feature = feature_df.dropna()\n", "feature = feature[selected_features]\n", "sc = StandardScaler()\n", "X_sc = sc.fit_transform(feature)\n", "X_sc = pd.DataFrame(X_sc, index=feature.index, columns=feature.columns)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#benchmark\n", "barrier_bm = pd.read_csv('C:data/barrier_bm.csv')\n", "barrier_bm.index = pd.to_datetime(barrier_bm.Date)\n", "barrier_bm.exit = pd.to_datetime(barrier_bm.exit)\n", "barrier_bm.drop(columns='Date',inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#labeling\n", "barrier = pd.read_csv('C:data/barrier.csv')\n", "barrier.index = pd.to_datetime(barrier.Date)\n", "barrier.exit = pd.to_datetime(barrier.exit)\n", "barrier.drop(columns='Date',inplace=True)\n", "\n", "rts = make_rt(close,barrier.dropna())\n", "outcome = rts.rt_returns\n", "outcome.index = rts.open_dt"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0    608\n", "0.0    421\n", "Name: rt_returns, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#meta-label\n", "wl = np.sign(np.sign(outcome)+1)\n", "y_ = wl\n", "y_.value_counts()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["loss = wl.value_counts()[0]\n", "win = wl.value_counts()[1]\n", "plt.figure(figsize=(10,3))\n", "plt.scatter(wl[wl==1].index,close.loc[wl[wl==1].index], alpha=0.5)\n", "plt.scatter(wl[wl==0].index,close.loc[wl[wl==0].index], marker='x', alpha=0.5)\n", "plt.legend(['win 1','lose 0'])\n", "plt.title('y (meta-label): win {}, lose {}'.format(win,loss))\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["raw_X = X_sc.copy()\n", "tmp = raw_X.join(y_).dropna()\n", "X=tmp.iloc[:,:-1]\n", "y=tmp.iloc[:,-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Construction"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Choose model\n", "\n", "# Cross Validation (k-fold)\n", "n_cv=4\n", "t1 = pd.to_datetime(barrier.exit.loc[X.index])\n", "cv = PKFold(n_cv,t1,0)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["SVC(C=10, probability=True)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Choose model (SVM-rbf)\n", "C = [0.1, 1,10]\n", "param_grid_rbf = dict(C=C)\n", "svc_rbf = SVC(kernel='rbf', probability=True)\n", "gs_svc_rbf = GridSearchCV(estimator=svc_rbf, param_grid= param_grid_rbf, cv=cv, scoring='precision')\n", "gs_svc_rbf.fit(X,y)\n", "svc_best = gs_svc_rbf.best_estimator_\n", "svc_best"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["RandomForestClassifier(n_estimators=200)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["n_estimators = [200,1000]\n", "#max_depth = [3,7]\n", "param_grid_rfc = dict(n_estimators=n_estimators)\n", "rfc = RandomForestClassifier()\n", "gs_rfc = GridSearchCV(estimator=rfc, param_grid= param_grid_rfc, cv=cv, scoring='precision')\n", "gs_rfc.fit(X,y)\n", "rfc_best = gs_rfc.best_estimator_\n", "rfc_best"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["AdaBoostClassifier(learning_rate=1, n_estimators=100)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["n_estimators_ab = [50,100]\n", "learning_rate = [1,0.1]\n", "param_grid_abc = dict(n_estimators=n_estimators_ab, learning_rate=learning_rate)\n", "\n", "abc=AdaBoostClassifier()\n", "gs_abc = GridSearchCV(estimator=abc, param_grid= param_grid_abc, cv=cv, scoring='precision')\n", "gs_abc.fit(X,y)\n", "ada_best = gs_abc.best_estimator_\n", "ada_best"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["GradientBoostingClassifier(learning_rate=0.01, n_estimators=200)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["n_estimators_gb = [100,200]\n", "learning_rate = [0.1,0.01]\n", "param_grid_gbc = dict(n_estimators=n_estimators_gb, learning_rate=learning_rate)\n", "gbc=GradientBoostingClassifier()\n", "gs_gbc = GridSearchCV(estimator=gbc, param_grid= param_grid_gbc, cv=cv, scoring='precision')\n", "gs_gbc.fit(X,y)\n", "gbc_best = gs_gbc.best_estimator_\n", "gbc_best"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["clf_list = [svc_best, rfc_best, ada_best, gbc_best]\n", "estimators=['SVM_best','RF_best','AdaBoost_best','GradientBoost_best']\n", "scores_list = []\n", "y_preds_list = []\n", "y_probs_list = []\n", "\n", "# for ML model prediction\n", "for clf in clf_list:\n", "    y_preds_ = []\n", "    y_probs_ = []\n", "\n", "    for train, test in cv.split(X, y):\n", "        clf.fit(<PERSON><PERSON>iloc[train], y.iloc[train])\n", "        y_true = y.iloc[test]\n", "        y_pred = clf.predict(<PERSON>.iloc[test])\n", "        y_probs = clf.predict_proba(X.iloc[test])\n", "        y_probs = y_probs[:, 1]\n", "        y_pred_series = pd.Series(y_pred,index=y[test].index)\n", "        y_probs_series = pd.Series(y_probs,index=y[test].index)\n", "        y_preds_.append(y_pred_series)\n", "        y_probs_.append(y_probs_series)\n", "    \n", "    \n", "    y_preds__ = pd.concat([i for i in y_preds_])\n", "    y_probs__ = pd.concat([i for i in y_probs_])\n", "    y_true__ = y.loc[y_preds__.index]\n", "    accs = accuracy_score(y_true__, y_preds__)\n", "    f1=f1_score(y_true__, y_preds__)\n", "    roc=roc_auc_score(y_true__, y_probs__)\n", "    prec=precision_score(y_true__, y_preds__)\n", "    score = [accs, f1, roc, prec]\n", "    scores_list.append(score)\n", "    y_preds_list.append(y_preds__)\n", "    y_probs_list.append(y_probs__)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["results = pd.DataFrame(scores_list, columns=['accuracy','f1 score','roc auc score','precision score'],index=estimators)\n", "result_show = results.sort_values('precision score', ascending=False)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accuracy</th>\n", "      <th>f1 score</th>\n", "      <th>roc auc score</th>\n", "      <th>precision score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AdaBoost_best</th>\n", "      <td>0.567541</td>\n", "      <td>0.631927</td>\n", "      <td>0.552471</td>\n", "      <td>0.635607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SVM_best</th>\n", "      <td>0.544218</td>\n", "      <td>0.585323</td>\n", "      <td>0.574228</td>\n", "      <td>0.632887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RF_best</th>\n", "      <td>0.549077</td>\n", "      <td>0.657817</td>\n", "      <td>0.537073</td>\n", "      <td>0.596257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GradientBoost_best</th>\n", "      <td>0.519922</td>\n", "      <td>0.609177</td>\n", "      <td>0.490364</td>\n", "      <td>0.586890</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    accuracy  f1 score  roc auc score  precision score\n", "AdaBoost_best       0.567541  0.631927       0.552471         0.635607\n", "SVM_best            0.544218  0.585323       0.574228         0.632887\n", "RF_best             0.549077  0.657817       0.537073         0.596257\n", "GradientBoost_best  0.519922  0.609177       0.490364         0.586890"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["result_show"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["y_probs_df = pd.DataFrame()\n", "for i in range(len(estimators)):\n", "    y_probs_df[estimators[i]] = y_probs_list[i]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["#평균\n", "pred_prob = pd.Series(y_probs_df.mean(axis=1),index=y_probs_df.index)\n", "\n", "#하나하나\n", "\n", "#y_probs_df_2 = y_probs_df[estimators[3]]\n", "#pred_prob = pd.Series(y_probs_df_2,index=y_probs_df_2.index)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["pred_prob2=pd.Series(normalize(pred_prob.to_frame().T).reshape(-1,), index=y_probs_df.index).rename('bet_confidence')"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["bet_confidence=pd.Series(MinMaxScaler().fit_transform(pred_prob2.to_frame()).reshape(-1,), index=y_probs_df.index).rename('bet_confidence')"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'counts')"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title('Bet confidence distribution')\n", "plt.hist(bet_confidence, bins=30)[2]\n", "plt.xlabel('Bet confidence')\n", "plt.ylabel('counts')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = close.loc[bet_confidence.index]\n", "plt.figure(figsize=(10,5))\n", "plt.title('Bet confidence')\n", "plt.plot(close, alpha=0.1)\n", "plt.scatter(c.index,c, c = bet_confidence, s=20,cmap='vlag',vmin=0,vmax=1)\n", "plt.colorbar()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Algo Trading Backtest"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["barrier_bm = barrier_bm.dropna()\n", "barrier_before = barrier.loc[bet_confidence.index].dropna()\n", "barrier_enhanced = barrier_before.loc[bet_confidence.loc[bet_confidence>0.5].index]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["rts_bm = make_rt(close,barrier_bm)\n", "rts_before = make_rt(close,barrier_before)\n", "rts_enhanced = make_rt(close,barrier_enhanced)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["result1 = pd.concat([round_trip.get_df_ann_sr(rts_bm,'Benchmark',years=11),\n", "                    round_trip.get_df_ann_sr(rts_before,'Trading Strategy (Primary)',years=11)],axis=1)\n", "\n", "df_sr = round_trip.get_df_ann_sr(rts_enhanced,'Enhanced Trading Strategy (Second)',years=11)\n", "result1 = result1.join(df_sr)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Benchmark</th>\n", "      <th>Trading Strategy (Primary)</th>\n", "      <th>Enhanced Trading Strategy (Second)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>avg_n_bets_per_year</th>\n", "      <td>246.272727</td>\n", "      <td>93.545455</td>\n", "      <td>49.636364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>win_ratio</th>\n", "      <td>0.520506</td>\n", "      <td>0.590467</td>\n", "      <td>0.612844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annualized_sharpe_ratio</th>\n", "      <td>0.538232</td>\n", "      <td>1.525995</td>\n", "      <td>1.623284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          Benchmark  Trading Strategy (Primary)  \\\n", "avg_n_bets_per_year      246.272727                   93.545455   \n", "win_ratio                  0.520506                    0.590467   \n", "annualized_sharpe_ratio    0.538232                    1.525995   \n", "\n", "                         Enhanced Trading Strategy (Second)  \n", "avg_n_bets_per_year                               49.636364  \n", "win_ratio                                          0.612844  \n", "annualized_sharpe_ratio                            1.623284  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["result1"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["result2 = pd.concat([round_trip.get_df_ann_sr(rts_bm,'Benchmark',years=11),\n", "                    round_trip.get_df_ann_sr(rts_before,'Trading Strategy (Primary)',years=11)],axis=1)\n", "winr = []\n", "for i in np.linspace(0.1,0.9,9):\n", "    barrier_enhanced_ = barrier_before.loc[bet_confidence.loc[bet_confidence>=i].index]\n", "    rts_enhanced_ = make_rt(close,barrier_enhanced_)\n", "    df_sr = round_trip.get_df_ann_sr(rts_enhanced_,'b',years=11)\n", "    winr.append(df_sr.T.win_ratio[0])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["dict_ = dict(zip(np.linspace(0.1,0.9,9).round(2),winr))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_res = pd.DataFrame.from_dict(dict_,orient='index')\n", "plt.figure(figsize=(10,5))\n", "plt.title(\"Hit-ratio of different thresholds strategy\")\n", "plt.bar(df_res.index, df_res[0], width=0.05)\n", "plt.plot(df_res)\n", "plt.ylabel('win ratio')\n", "plt.xlabel('bet confidence threshold')\n", "plt.ylim(0.5,0.8)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}