{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Trading rules: only long strategy\n", "- inputs\n", "    - Enter rules: momentum signals & TA \n", "    - Exit rules: dynamic triple exit rule\n", "\n", "- outputs\n", "    - outcome of strategy's trading simulation\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-11-01T12:11:45.835118Z", "start_time": "2021-11-01T12:11:45.822154Z"}}, "outputs": [], "source": ["# lib\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns;sns.set()\n", "plt.style.use('tableau-colorblind10')\n", "\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, normalize\n", "\n", "# homemade\n", "from features import trnd_scan, tautil\n", "from labeling import labeling\n", "from triple_barrier import get_barrier, make_rt\n", "from backtest import print_round_trip_stats, round_trip"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-11-01T12:06:39.285566Z", "start_time": "2021-11-01T12:06:39.270607Z"}}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(action='ignore')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["market_df = pd.read_csv('C:data/market_samsung.csv')\n", "market_df = market_df.rename(columns={market_df.columns[0]:'Date'})\n", "market_df.index = pd.to_datetime(market_df.Date)\n", "market_df.drop(columns='Date',inplace=True)\n", "market_df.dropna(inplace=True)\n", "\n", "close = market_df.close['2010':'2020']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Trading Rules"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enter rules"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Momentum Prediction"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["signals = pd.read_csv('C:data/momentum_signals.csv')\n", "signals.index = pd.to_datetime(signals['Date'])\n", "signals.drop(columns='Date',inplace=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["signals = signals['signals'].loc['2010':'2020']"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["scaler = normalize\n", "scaler2 = MinMaxScaler()\n", "signals = pd.Series(scaler2.fit_transform(normalize(signals.values.reshape(-1,1),axis=0)).reshape((-1,)), \n", "                           index=signals.index).rename('signals')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(signals,bins=50)[2]\n", "plt.title('Distribution of momentum signals')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["thresholds = [0, 0.3]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["enter_ml_list=[]\n", "for h in thresholds:\n", "    enter_ml_list.append(signals.loc[signals>h].index)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(len(thresholds)):\n", "    plt.figure(figsize=(10,3))\n", "    plt.plot(close, alpha=0.5)\n", "    plt.title('Enter points ML Prediction (Option {})'.format(i+1))\n", "    plt.plot(close.loc[enter_ml_list[i]],marker='^',linewidth=0,alpha=0.3)\n", "    plt.legend(['price','Long signals from momentum classifier'])\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tech. Analysis Long/short decision"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-11-01T12:17:40.675305Z", "start_time": "2021-11-01T12:17:40.648394Z"}}, "outputs": [], "source": ["open = market_df.open['2010':'2020']\n", "rsi = tautil.RSIIndicator(open,14).rsi().dropna()\n", "long = (rsi>=50) & (rsi<70)\n", "enter_ta = rsi.loc[long].index"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,3))\n", "plt.plot(close, alpha=0.5)\n", "plt.title('Enter points TA')\n", "plt.plot(close.loc[enter_ta],marker='^',linewidth=0,alpha=0.3)\n", "plt.legend(['price','Long signals from rsi'])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["enter_list = [enter_ta]\n", "enter_list.append((enter_ml_list[1]& enter_ta).sort_values().drop_duplicates())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(len(thresholds)):\n", "    plt.figure(figsize=(10,3))\n", "    plt.plot(close, alpha=0.5)\n", "    plt.title('Enter points (Option {})'.format(i+1))\n", "    plt.plot(close.loc[enter_list[i]],marker='^',linewidth=0,alpha=0.3)\n", "    plt.legend(['price','Enter points'])\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,3))\n", "plt.plot(close, alpha=0.5)\n", "plt.title('Position Enter points'.format(i+1))\n", "plt.plot(close.loc[enter_list[1]],marker='^',linewidth=0,alpha=0.3)\n", "plt.legend(['price','Enter points'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exit rules"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# no Rule (benchmark)\n", "pt_sl_bm = [1000,1000]\n", "max_holding_bm = [1, 0]\n", "no_exit_rule = [pt_sl_bm,max_holding_bm]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["#dynamic target rule\n", "max_holding = [60, 0]\n", "close_ = market_df.close['2009':'2020']\n", "changes = close_.pct_change(1).to_frame()\n", "for i in range(2,max_holding[0]+1):\n", "    changes = changes.join(close_.pct_change(i).rename('close {}'.format(i)))\n", "dynamic_target = changes.abs().dropna().mean(axis=1)['2010':]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["barrier_exit_list=[]\n", "barrier_exit_list.append(get_barrier(close, enter_list[1], [1,1], max_holding, target = dynamic_target))  #dynamic  \n", "\n", "rts_exit_list=[]\n", "for i in range(len(barrier_exit_list)):\n", "    rts_exit_list.append(make_rt(close,barrier_exit_list[i].dropna()))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1fe01fa7148>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x144 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,2))\n", "plt.title('Dynamic exit rule returns')\n", "plt.plot(barrier_exit_list[0].ret)\n", "plt.legend(['Returns of dynamic exit target rate'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Results\n", "## Benchmarks"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["barrier_bm = get_barrier(close, close.index, no_exit_rule[0], no_exit_rule[1]) #no rule"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["rts_bm = make_rt(close,barrier_bm.dropna())"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Benchmark</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>avg_n_bets_per_year</th>\n", "      <td>243.272727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>win_ratio</th>\n", "      <td>0.518835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annualized_sharpe_ratio</th>\n", "      <td>0.537080</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          Benchmark\n", "avg_n_bets_per_year      243.272727\n", "win_ratio                  0.518835\n", "annualized_sharpe_ratio    0.537080"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["round_trip.get_df_ann_sr(rts_bm,'Benchmark',years=11)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>No Rule</th>\n", "      <th>Enter &amp; Exit Rule</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>avg_n_bets_per_year</th>\n", "      <td>243.272727</td>\n", "      <td>105.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>win_ratio</th>\n", "      <td>0.518835</td>\n", "      <td>0.590988</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annualized_sharpe_ratio</th>\n", "      <td>0.537080</td>\n", "      <td>1.800316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            No Rule  Enter & Exit Rule\n", "avg_n_bets_per_year      243.272727         105.000000\n", "win_ratio                  0.518835           0.590988\n", "annualized_sharpe_ratio    0.537080           1.800316"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df = pd.concat([round_trip.get_df_ann_sr(rts_bm,'No Rule')], axis=1)\n", "for i in range(len(rts_exit_list)):\n", "    result_df = result_df.join(round_trip.get_df_ann_sr(rts_exit_list[i],'Enter & Exit Rule'))\n", "    \n", "result_df"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON> holding 20 days</th>\n", "      <th><PERSON><PERSON> holding 60 days</th>\n", "      <th><PERSON><PERSON> holding 120 days</th>\n", "      <th><PERSON><PERSON> holding 260 days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>No Rule</th>\n", "      <td>0.518835</td>\n", "      <td>0.518835</td>\n", "      <td>0.518835</td>\n", "      <td>0.518835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>0.541054</td>\n", "      <td>0.576857</td>\n", "      <td>0.572169</td>\n", "      <td>0.575130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>0.564014</td>\n", "      <td>0.573898</td>\n", "      <td>0.570069</td>\n", "      <td>0.570441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>0.532872</td>\n", "      <td>0.559689</td>\n", "      <td>0.553155</td>\n", "      <td>0.550562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>0.555363</td>\n", "      <td>0.588591</td>\n", "      <td>0.589455</td>\n", "      <td>0.587727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>0.564991</td>\n", "      <td>0.605195</td>\n", "      <td>0.605195</td>\n", "      <td>0.603463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>0.562392</td>\n", "      <td>0.605195</td>\n", "      <td>0.600866</td>\n", "      <td>0.595671</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>0.557192</td>\n", "      <td>0.600000</td>\n", "      <td>0.584416</td>\n", "      <td>0.586147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>0.555459</td>\n", "      <td>0.599134</td>\n", "      <td>0.598787</td>\n", "      <td>0.601732</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>0.563258</td>\n", "      <td>0.587522</td>\n", "      <td>0.600520</td>\n", "      <td>0.599653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>0.555459</td>\n", "      <td>0.575022</td>\n", "      <td>0.595486</td>\n", "      <td>0.604510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>0.561525</td>\n", "      <td>0.585281</td>\n", "      <td>0.611785</td>\n", "      <td>0.620451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>0.560659</td>\n", "      <td>0.593560</td>\n", "      <td>0.608014</td>\n", "      <td>0.620209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>0.559792</td>\n", "      <td>0.601739</td>\n", "      <td>0.607485</td>\n", "      <td>0.623151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>0.555844</td>\n", "      <td>0.579496</td>\n", "      <td>0.578261</td>\n", "      <td>0.597391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>0.545927</td>\n", "      <td>0.574413</td>\n", "      <td>0.581010</td>\n", "      <td>0.600174</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>0.558925</td>\n", "      <td>0.598432</td>\n", "      <td>0.598082</td>\n", "      <td>0.625981</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>0.551127</td>\n", "      <td>0.586297</td>\n", "      <td>0.594266</td>\n", "      <td>0.615451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>0.548918</td>\n", "      <td>0.594805</td>\n", "      <td>0.601386</td>\n", "      <td>0.629116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>0.548918</td>\n", "      <td>0.591342</td>\n", "      <td>0.606586</td>\n", "      <td>0.623050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>0.535065</td>\n", "      <td>0.596886</td>\n", "      <td>0.612987</td>\n", "      <td>0.627706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>0.555844</td>\n", "      <td>0.614187</td>\n", "      <td>0.628571</td>\n", "      <td>0.643290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>230</th>\n", "      <td>0.552768</td>\n", "      <td>0.595506</td>\n", "      <td>0.605195</td>\n", "      <td>0.624567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>0.561525</td>\n", "      <td>0.618387</td>\n", "      <td>0.629887</td>\n", "      <td>0.647569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>0.559792</td>\n", "      <td>0.609714</td>\n", "      <td>0.625543</td>\n", "      <td>0.644097</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Max. holding 20 days  Max. holding 60 days  Max. holding 120 days  \\\n", "No Rule              0.518835              0.518835               0.518835   \n", "20                   0.541054              0.576857               0.572169   \n", "30                   0.564014              0.573898               0.570069   \n", "40                   0.532872              0.559689               0.553155   \n", "50                   0.555363              0.588591               0.589455   \n", "60                   0.564991              0.605195               0.605195   \n", "70                   0.562392              0.605195               0.600866   \n", "80                   0.557192              0.600000               0.584416   \n", "90                   0.555459              0.599134               0.598787   \n", "100                  0.563258              0.587522               0.600520   \n", "110                  0.555459              0.575022               0.595486   \n", "120                  0.561525              0.585281               0.611785   \n", "130                  0.560659              0.593560               0.608014   \n", "140                  0.559792              0.601739               0.607485   \n", "150                  0.555844              0.579496               0.578261   \n", "160                  0.545927              0.574413               0.581010   \n", "170                  0.558925              0.598432               0.598082   \n", "180                  0.551127              0.586297               0.594266   \n", "190                  0.548918              0.594805               0.601386   \n", "200                  0.548918              0.591342               0.606586   \n", "210                  0.535065              0.596886               0.612987   \n", "220                  0.555844              0.614187               0.628571   \n", "230                  0.552768              0.595506               0.605195   \n", "240                  0.561525              0.618387               0.629887   \n", "250                  0.559792              0.609714               0.625543   \n", "\n", "         Max. holding 260 days  \n", "No Rule               0.518835  \n", "20                    0.575130  \n", "30                    0.570441  \n", "40                    0.550562  \n", "50                    0.587727  \n", "60                    0.603463  \n", "70                    0.595671  \n", "80                    0.586147  \n", "90                    0.601732  \n", "100                   0.599653  \n", "110                   0.604510  \n", "120                   0.620451  \n", "130                   0.620209  \n", "140                   0.623151  \n", "150                   0.597391  \n", "160                   0.600174  \n", "170                   0.625981  \n", "180                   0.615451  \n", "190                   0.629116  \n", "200                   0.623050  \n", "210                   0.627706  \n", "220                   0.643290  \n", "230                   0.624567  \n", "240                   0.647569  \n", "250                   0.644097  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["#dynamic target rule\n", "# different maximum holding\n", "close_ = market_df.close['2009':'2020']\n", "rolling = np.arange(20,260,10)\n", "mhs = [20,60,120,260]\n", "win_ratios = pd.DataFrame()\n", "\n", "for mh in mhs:\n", "    max_holding = [mh, 0]\n", "    dynamic_targets = []\n", "    for j in rolling:\n", "        for i in range(2,j+1):\n", "            changes = close_.pct_change(1).to_frame()\n", "            changes = changes.join(close_.pct_change(i).rename('close {}'.format(i)))\n", "        dynamic_target = changes.abs().dropna().mean(axis=1)['2010':]\n", "        dynamic_targets.append(dynamic_target)\n", "\n", "    barrier_exit_list_rolling=[]\n", "    for i in range(len(dynamic_targets)):\n", "        barrier_exit_list_rolling.append(get_barrier(close, enter_list[1], [1,1], max_holding, target = dynamic_targets[i]))  #dynamic  \n", "\n", "    rts_exit_list=[]\n", "    for i in range(len(barrier_exit_list_rolling)):\n", "        rts_exit_list.append(make_rt(close,barrier_exit_list_rolling[i].dropna()))\n", "\n", "    result_df = pd.concat([round_trip.get_df_ann_sr(rts_bm,'No Rule')], axis=1)\n", "    for i in range(len(rts_exit_list)):\n", "        result_df = result_df.join(round_trip.get_df_ann_sr(rts_exit_list[i],'{}'.format(rolling[i])))\n", "\n", "    win_ratios['Max. holding {} days'.format(mh)] = result_df.T.win_ratio\n", "win_ratios"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15,6))\n", "plt.title(\"Exit rules\")\n", "plt.plot(win_ratios)\n", "plt.legend(win_ratios)\n", "plt.ylabel('win ratio')\n", "plt.xlabel('Rolling days for calculating target rate')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# For next research"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["barrier = get_barrier(close, enter_list[1], [1,1], max_holding, target = dynamic_target)\n", "rts = make_rt(close,barrier.dropna())"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Chose for 2nd model</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>avg_n_bets_per_year</th>\n", "      <td>104.818182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>win_ratio</th>\n", "      <td>0.644097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>annualized_sharpe_ratio</th>\n", "      <td>3.122372</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         Chose for 2nd model\n", "avg_n_bets_per_year               104.818182\n", "win_ratio                           0.644097\n", "annualized_sharpe_ratio             3.122372"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["round_trip.get_df_ann_sr(rts,'Chose for 2nd model')"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Summary stats</th>\n", "      <th>All trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total number of round_trips</th>\n", "      <td>1153.00</td>\n", "      <td>1153.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Percent profitable</th>\n", "      <td>0.64</td>\n", "      <td>0.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Winning round_trips</th>\n", "      <td>742.00</td>\n", "      <td>742.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Losing round_trips</th>\n", "      <td>410.00</td>\n", "      <td>410.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Even round_trips</th>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>PnL stats</th>\n", "      <th>All trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total profit</th>\n", "      <td>$1562060.00</td>\n", "      <td>$1562060.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross profit</th>\n", "      <td>$2900830.00</td>\n", "      <td>$2900830.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross loss</th>\n", "      <td>$-1338770.00</td>\n", "      <td>$-1338770.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Profit factor</th>\n", "      <td>$2.17</td>\n", "      <td>$2.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. trade net profit</th>\n", "      <td>$1354.78</td>\n", "      <td>$1354.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. winning trade</th>\n", "      <td>$3909.47</td>\n", "      <td>$3909.47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. losing trade</th>\n", "      <td>$-3265.29</td>\n", "      <td>$-3265.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ratio Avg. Win:Avg. Loss</th>\n", "      <td>$1.20</td>\n", "      <td>$1.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>$15720.00</td>\n", "      <td>$15720.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>$-19350.00</td>\n", "      <td>$-19350.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Duration stats</th>\n", "      <th>All trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg duration</th>\n", "      <td>98 days 20:41:25.342584562</td>\n", "      <td>98 days 20:41:25.342584562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median duration</th>\n", "      <td>51 days 00:00:00</td>\n", "      <td>51 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Longest duration</th>\n", "      <td>264 days 00:00:00</td>\n", "      <td>264 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shortest duration</th>\n", "      <td>1 days 00:00:00</td>\n", "      <td>1 days 00:00:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Return stats</th>\n", "      <th>All trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>0.22%</td>\n", "      <td>0.22%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>1.38%</td>\n", "      <td>1.38%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-1.22%</td>\n", "      <td>-1.22%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>0.18%</td>\n", "      <td>0.18%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>1.06%</td>\n", "      <td>1.06%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.99%</td>\n", "      <td>-0.99%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>8.69%</td>\n", "      <td>8.69%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-9.44%</td>\n", "      <td>-9.44%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Symbol stats</th>\n", "      <th>Asset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>0.22%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>1.38%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-1.22%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>0.18%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>1.06%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.99%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>8.69%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-9.44%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print_round_trip_stats(rts)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["barrier.to_csv('C:data/barrier.csv')"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["barrier_bm.to_csv('C:data/barrier_bm.csv')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}