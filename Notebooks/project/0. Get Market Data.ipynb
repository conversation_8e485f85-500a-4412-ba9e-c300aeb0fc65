{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# lib\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns;sns.set()\n", "\n", "import FinanceDataReader as fdr\n", "import yfinance as yf\n", "df_ohlc = fdr.<PERSON>R<PERSON>er('005930','2005-7-27','2022-1-17').iloc[:,0:4] #가격 수정되어 있음\n", "volume_yf = yf.download('005930.KS','2005-7-27','2022-1-17',auto_adjust=True).Volume # 수정거래량\n", "\n", "df_ohlcv = df_ohlc.join(volume_yf).dropna()\n", "df = tautil.ohlcv(df_ohlcv)\n", "\n", "quantity_ = pd.read_csv('C:data/순매수량.csv')\n", "quantity_ = quantity_.iloc[:-1,1:5]\n", "quantity_.columns = ['Date','individual','foreign','institutional']\n", "quantity_.index = quantity_['Date'].apply(lambda x: pd.to_datetime(str(x), format='%Y%m%d'))\n", "quantity_.drop(columns='Date',inplace=True)\n", "\n", "df = df.join(quantity_).dropna()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}