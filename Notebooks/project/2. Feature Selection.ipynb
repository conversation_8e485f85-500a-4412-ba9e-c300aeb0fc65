{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Feature Selection\n", "- Input\n", "    - label: up-trend vs. (down or no trend)\n", "    - periods : 2005-2010\n", "    - features: market data features\n", "\n", "- Model: RF\n", "    - 5 Feature Selection Methods: original, mda-kmeans, mda-optics, mda-onc, rfecv, sbfs(too much cost)\n", "\n", "- Output\n", "    - best methods & selected features"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:06:20.955568Z", "start_time": "2021-11-06T07:06:20.781355Z"}}, "outputs": [], "source": ["# lib\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns;sns.set()\n", "plt.style.use('tableau-colorblind10')\n", "\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import roc_auc_score, accuracy_score, f1_score\n", "\n", "from sklearn.cluster import OPTICS, KMeans\n", "from sklearn.metrics import silhouette_score\n", "from sklearn.feature_selection import RFECV, SequentialFeatureSelector\n", "\n", "# homemade\n", "from feature_engineering import cluster\n", "from feature_importance import importance\n", "from labeling import labeling\n", "from mlutil.pkfold import PKFold"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:06:21.855360Z", "start_time": "2021-11-06T07:06:21.844363Z"}, "scrolled": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(action='ignore')"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["market_df = pd.read_csv('C:data/market_samsung.csv')\n", "market_df = market_df.rename(columns={market_df.columns[0]:'Date'})\n", "market_df.index = pd.to_datetime(market_df.Date)\n", "market_df.drop(columns='Date',inplace=True)\n", "market_df.dropna(inplace=True)\n", "\n", "feature_df = pd.read_csv('C:data/features_samsung.csv')\n", "feature_df = feature_df.rename(columns={feature_df.columns[0]:'Date'})\n", "feature_df.index = pd.to_datetime(feature_df.Date)\n", "feature_df.drop(columns='Date',inplace=True)\n", "feature_df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:07:13.931903Z", "start_time": "2021-11-06T07:07:13.916273Z"}}, "outputs": [], "source": ["X = feature_df.dropna()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:07:14.449727Z", "start_time": "2021-11-06T07:07:14.429352Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 3873 entries, 2005-11-04 to 2021-10-15\n", "Data columns (total 32 columns):\n", " #   Column                    Non-Null Count  Dtype  \n", "---  ------                    --------------  -----  \n", " 0   momentum_rsi_15           3873 non-null   float64\n", " 1   momentum_wr_15            3873 non-null   float64\n", " 2   trend_adx_15              3873 non-null   float64\n", " 3   trend_aroon_ind_20        3873 non-null   float64\n", " 4   trend_dpo_20              3873 non-null   float64\n", " 5   trend_macd_diff_25_10_9   3873 non-null   float64\n", " 6   trend_mass_index_10_25    3873 non-null   float64\n", " 7   trend_trix_15             3873 non-null   float64\n", " 8   volatility_atr_10         3873 non-null   float64\n", " 9   volatility_ui_15          3873 non-null   float64\n", " 10  volume_cmf_20             3873 non-null   float64\n", " 11  volume_fi_15              3873 non-null   float64\n", " 12  volume_mfi_15             3873 non-null   float64\n", " 13  volume_sma_em_15          3873 non-null   float64\n", " 14  volume_vpt                3873 non-null   float64\n", " 15  ret_10                    3873 non-null   float64\n", " 16  ret_20                    3873 non-null   float64\n", " 17  ret_5                     3873 non-null   float64\n", " 18  std_30                    3873 non-null   float64\n", " 19  individual sma_5          3873 non-null   float64\n", " 20  individual sma_20         3873 non-null   float64\n", " 21  foreign sma_5             3873 non-null   float64\n", " 22  foreign sma_20            3873 non-null   float64\n", " 23  institutional sma_5       3873 non-null   float64\n", " 24  institutional sma_20      3873 non-null   float64\n", " 25  trend_back_scan_20        3873 non-null   float64\n", " 26  trend_back_scan_60        3873 non-null   float64\n", " 27  kyle_lambda               3873 non-null   float64\n", " 28  amihud_lambda             3873 non-null   float64\n", " 29  hasbrouck_lambda          3873 non-null   float64\n", " 30  bekker_parkinson_vol      3873 non-null   float64\n", " 31  cor<PERSON>_schultz_estimator  3873 non-null   float64\n", "dtypes: float64(32)\n", "memory usage: 998.5 KB\n"]}], "source": ["X.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Feature selection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clustering Methods"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### clustering"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:07:18.248269Z", "start_time": "2021-11-06T07:07:18.236277Z"}}, "outputs": [], "source": ["from sklearn.preprocessing import StandardScaler\n", "sc = StandardScaler()\n", "X_sc = sc.fit_transform(X)\n", "X_sc = pd.DataFrame(X_sc, index=X.index, columns=X.columns)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["X_sc=X_sc[:'2020']"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:07:19.452157Z", "start_time": "2021-11-06T07:07:18.837293Z"}}, "outputs": [], "source": ["silhouette_coefficients = []\n", "kmeans_kwargs = {\n", "    \"init\": \"random\",\n", "    \"n_init\": 10,\n", "    \"max_iter\": 300,\n", "    \"random_state\": 42,\n", "}\n", "\n", "for k in range(2, 30):\n", "    kmeans = KMeans(n_clusters=k, **kmeans_kwargs)\n", "    kmeans.fit(X.T)\n", "    score = silhouette_score(X.T, kmeans.labels_)\n", "    silhouette_coefficients.append(score)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:07:28.075037Z", "start_time": "2021-11-06T07:07:27.989415Z"}}, "outputs": [], "source": ["n_clusters=np.argmin(silhouette_coefficients)+2\n", "kmeans = KMeans(\n", "    init=\"random\",\n", "    n_clusters=n_clusters,\n", "    n_init=10,\n", "    max_iter=300,\n", "    random_state=42)\n", "kmeans.fit(X_sc.T)\n", "clusters_kmeans = {i: X_sc.columns[np.where(kmeans.labels_ == i)[0]].tolist() for i in np.unique(kmeans.labels_)}"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["optics = OPTICS(min_cluster_size=2)\n", "optics.fit(X.T)\n", "clusters_optics = {i: X_sc.columns[np.where(optics.labels_ == i)[0]].tolist() for i in np.unique(optics.labels_)}"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No feature/s found with low silhouette score. All features belongs to its respective clusters\n"]}], "source": ["# 오래 걸림.\n", "clusters_onc_dist = cluster.get_feature_clusters(X_sc, dependence_metric= 'distance_correlation')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### mda - selection"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:20:03.162545Z", "start_time": "2021-11-06T07:19:04.236938Z"}}, "outputs": [], "source": ["#labeling\n", "trend_scanning_window = 60\n", "trend_scanning_q = 3\n", "ts_out = labeling.trend_scanning_label(market_df.close, window = trend_scanning_window, q = trend_scanning_q)\n", "mom_label = ts_out[0]\n", "y = np.sign(mom_label-1)+1 # up-trend vs others"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:20:03.178375Z", "start_time": "2021-11-06T07:20:03.162545Z"}}, "outputs": [], "source": ["raw_X = X_sc.copy()\n", "\n", "tmp = raw_X.join(y).dropna()\n", "X=tmp.iloc[:,:-1]\n", "y=tmp.iloc[:,-1]\n", "\n", "# train & test split\n", "# use previous data for feature selection\n", "X = X.loc['2005':'2010']\n", "y = y.loc['2005':'2010']"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:20:03.210287Z", "start_time": "2021-11-06T07:20:03.181366Z"}}, "outputs": [], "source": ["# CV\n", "n_cv=4\n", "t1 = ts_out[1].loc[X.index]\n", "cv = PKFold(n_cv,t1,0.01)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["clusters = [clusters_kmeans[i] for i in range(n_clusters)]\n", "clusters2 = [clusters_optics[i] for i in clusters_optics.keys()]\n", "clusters3 = clusters_onc_dist"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:21:38.781197Z", "start_time": "2021-11-06T07:20:59.059244Z"}}, "outputs": [], "source": ["clf = RandomForestClassifier(n_estimators=1000,class_weight='balanced')\n", "mda_cluster = importance.mean_decrease_accuracy(clf,X,y,cv,clustered_subsets=clusters)\n", "mda_cluster2 = importance.mean_decrease_accuracy(clf,X,y,cv,clustered_subsets=clusters2)\n", "mda_cluster3 = importance.mean_decrease_accuracy(clf,X,y,cv,clustered_subsets=clusters3)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["features_mda_kmeans = mda_cluster.loc[mda_cluster['mean'] == mda_cluster['mean'].max()].index\n", "features_mda_optics = mda_cluster2.loc[mda_cluster2['mean'] == mda_cluster2['mean'].max()].index\n", "features_mda_onc_dist = mda_cluster3.loc[mda_cluster3['mean'] == mda_cluster3['mean'].max()].index\n", "\n", "# 0에서 min 값으로 변경함."]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["new_X1 = X[features_mda_kmeans]\n", "new_X2 = X[features_mda_optics]\n", "new_X3 = X[features_mda_onc_dist]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Non-clustering methods\n", "\n", "- rfecv/ sbfs"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["# 오래걸림\n", "\n", "rf = RandomForestClassifier(class_weight='balanced')\n", "\n", "min_features_to_select = 2  # Minimum number of features to consider\n", "rfecv = RFECV(\n", "    estimator=rf,\n", "    step=1,\n", "    cv=cv,\n", "    scoring=\"accuracy\",\n", "    min_features_to_select=min_features_to_select,\n", ")\n", "new_X5_ = rfecv.fit_transform(X,y)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["new_X5 = pd.DataFrame(new_X5_, index=X.index, columns=rfecv.get_feature_names_out())"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["X_list = [X,new_X1,new_X2,new_X3,new_X5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# results"]}, {"cell_type": "code", "execution_count": 84, "metadata": {"ExecuteTime": {"end_time": "2021-11-06T07:22:09.651645Z", "start_time": "2021-11-06T07:21:39.268311Z"}}, "outputs": [], "source": ["clf = RandomForestClassifier(class_weight='balanced')\n", "score_list = []\n", "for X_ in X_list:\n", "    accs = []\n", "    f1 = []\n", "    roc_auc = []\n", "\n", "    for train, test in cv.split(X_, y):\n", "        clf.fit(X_.iloc[train], y.iloc[train])\n", "        y_true = y.iloc[test]\n", "        y_pred = clf.predict(X_.iloc[test])\n", "        y_probs = clf.predict_proba(X_.iloc[test])\n", "        y_probs = y_probs[:, 1]\n", "        accs.append(accuracy_score(y_true, y_pred))\n", "        f1.append(f1_score(y_true, y_pred))\n", "        roc_auc.append(roc_auc_score(y_true, y_probs))\n", "\n", "\n", "    accs = np.mean(accs)\n", "    f1 = np.mean(f1)\n", "    roc = np.mean(roc_auc)\n", "    scores = [accs, f1, roc]\n", "    score_list.append(scores)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["result_df = pd.DataFrame(score_list,\n", "            columns=['accuracy','f1 score','roc auc score'], \n", "             index = ['original','mda_kmeans','mda_optics','mda_onc','rfecv'])"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accuracy</th>\n", "      <th>f1 score</th>\n", "      <th>roc auc score</th>\n", "      <th>mean_</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>rfecv</th>\n", "      <td>0.641745</td>\n", "      <td>0.279223</td>\n", "      <td>0.535976</td>\n", "      <td>0.485648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>original</th>\n", "      <td>0.630062</td>\n", "      <td>0.243987</td>\n", "      <td>0.510087</td>\n", "      <td>0.461379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mda_kmeans</th>\n", "      <td>0.596573</td>\n", "      <td>0.251614</td>\n", "      <td>0.504474</td>\n", "      <td>0.450887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mda_onc</th>\n", "      <td>0.592679</td>\n", "      <td>0.196722</td>\n", "      <td>0.464315</td>\n", "      <td>0.417905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mda_optics</th>\n", "      <td>0.591900</td>\n", "      <td>0.156097</td>\n", "      <td>0.479004</td>\n", "      <td>0.409001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            accuracy  f1 score  roc auc score     mean_\n", "rfecv       0.641745  0.279223       0.535976  0.485648\n", "original    0.630062  0.243987       0.510087  0.461379\n", "mda_kmeans  0.596573  0.251614       0.504474  0.450887\n", "mda_onc     0.592679  0.196722       0.464315  0.417905\n", "mda_optics  0.591900  0.156097       0.479004  0.409001"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df['mean_'] = result_df.mean(axis=1)\n", "result_df.sort_values('mean_', ascending=False)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['trend_adx_15', 'trend_mass_index_10_25', 'trend_trix_15',\n", "       'volatility_atr_10', 'volatility_ui_15', 'volume_cmf_20',\n", "       'volume_mfi_15', 'std_30', 'individual sma_20', 'foreign sma_20',\n", "       'institutional sma_20', 'trend_back_scan_60', 'kyle_lambda',\n", "       'amihud_lambda', 'hasbrouck_lambda'],\n", "      dtype='object')"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["#best features\n", "selected_features = X_list[result_df['mean_'].argmax()].iloc[0:2]\n", "selected_features.columns"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["selected_features.to_csv('C:data/selected_features.csv')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}