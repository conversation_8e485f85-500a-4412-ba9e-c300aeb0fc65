# 使用机器学习的交易规则
这是我使用机器学习进行金融交易的项目。

- [示例](https://github.com/jo-cho/trading-rules-using-machine-learning/blob/main/Notebooks/ETHUSD%20trading%20ML.ipynb)
- [项目](https://github.com/jo-cho/trading-rules-using-machine-learning/tree/main/Notebooks/project)

动量预测和利用机器学习增强策略

1. 金融数据和交易栏
    - 使用tick数据形成时间/美元栏

2. 获取买入/卖出信号
    - 动量策略 (RSI..)
    - 附加的机器学习制度检测器

3. 交易规则
    - 使用分类器的交易信号设置进入规则
    - 使用获取利润、止损率和最大持有期的退出规则
    - (用于增强策略) 对二元结果进行标记 (盈利或亏损)

4. 增强策略的机器学习模型

- 获取特征 (X)
    - 市场数据和技术分析
    - 微观结构特征
    - 宏观经济变量
    - 基本面
    - *新闻/舆情* (进行中)

- 特征工程
    - 特征选择，降维

- 机器学习模型优化
    - 交叉验证 (时间序列交叉验证 / 清除折叠)
    - 超参数调整
    - 使用autogluon进行自动机器学习 (或者简单地使用随机森林、LightGBM或XGBoost等集成方法)
    - 度量标准 (准确率、F1分数、ROC-AUC)

- 结果
    - 下注信心 (接受单一交易信号的概率)

4. 交易决策
    - 对动量策略的每个交易信号决定下注或放弃。上述的机器学习模型会对你有所帮助。
    - 使用一些先进模型进行下注规模设定 (进行中)

5. 回测
    - 累积收益、夏普比率、最大回撤、胜率

# 参考文献:
- 《金融机器学习的进展》，Lopez de Prado (2018)

# 流程图
![ML交易网络](https://user-images.githubusercontent.com/52461409/132567663-eeead1ab-d3de-4cf3-a79f-6fea94722999.png)
